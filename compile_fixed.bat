
@echo off
echo ======================================
echo  Fixed Comprehensive Phase 1 LaTeX Compiler
echo ======================================
echo.

echo Checking for LaTeX installation...
where pdflatex >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: pdflatex not found. Please install a LaTeX distribution.
    echo   - MiKTeX: https://miktex.org/
    echo   - TeX Live: https://www.tug.org/texlive/
    pause
    exit /b 1
)

echo LaTeX found. Compiling fixed comprehensive document...
echo.

echo First compilation pass...
pdflatex -interaction=nonstopmode phase1_comprehensive_full_fixed.tex
if %errorlevel% neq 0 (
    echo WARNING: First compilation had issues. Continuing...
)

echo Second compilation pass (for cross-references)...
pdflatex -interaction=nonstopmode phase1_comprehensive_full_fixed.tex
if %errorlevel% neq 0 (
    echo WARNING: Second compilation had issues. Continuing...
)

echo Third compilation pass (for final formatting)...
pdflatex -interaction=nonstopmode phase1_comprehensive_full_fixed.tex

echo.
echo ======================================
echo  Compilation Complete!
echo ======================================
echo.

if exist phase1_comprehensive_full_fixed.pdf (
    echo SUCCESS: phase1_comprehensive_full_fixed.pdf has been created!
    echo.
    echo Document features:
    echo   - Fixed text flow (no awkward line breaks)
    echo   - Professional formatting
    echo   - All original content preserved
    echo   - High-quality figures included
    echo.
    echo Cleaning up auxiliary files...
    del *.aux *.log *.out *.toc *.fls *.fdb_latexmk 2>nul
    echo.
    echo Opening PDF...
    start phase1_comprehensive_full_fixed.pdf
) else (
    echo ERROR: PDF was not created. Check the log file for errors.
    echo.
    echo Troubleshooting tips:
    echo   - Ensure all figure files are present
    echo   - Check LaTeX log for missing packages
    echo   - Verify LaTeX installation is complete
)

pause
