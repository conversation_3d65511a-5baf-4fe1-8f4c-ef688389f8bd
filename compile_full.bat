
@echo off
echo ======================================
echo  Enhanced Comprehensive Phase 1 LaTeX Compiler
echo ======================================
echo.

echo Checking for LaTeX installation...
where pdflatex >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: pdflatex not found. Please install a LaTeX distribution.
    pause
    exit /b 1
)

echo LaTeX found. Compiling comprehensive document...
echo.

echo First compilation pass...
pdflatex -interaction=nonstopmode phase1_comprehensive_full.tex
if %errorlevel% neq 0 (
    echo WARNING: First compilation had issues. Check log for details.
)

echo Second compilation pass...
pdflatex -interaction=nonstopmode phase1_comprehensive_full.tex
if %errorlevel% neq 0 (
    echo WARNING: Second compilation had issues. Check log for details.
)

echo Third compilation pass...
pdflatex -interaction=nonstopmode phase1_comprehensive_full.tex

echo.
echo ======================================
echo  Compilation Complete!
echo ======================================
echo.

if exist phase1_comprehensive_full.pdf (
    echo SUCCESS: phase1_comprehensive_full.pdf has been created!
    echo.
    echo Cleaning up auxiliary files...
    del *.aux *.log *.out *.toc *.fls *.fdb_latexmk 2>nul
    echo.
    echo Opening PDF...
    start phase1_comprehensive_full.pdf
) else (
    echo ERROR: PDF was not created. Check the log file for errors.
    echo Common issues:
    echo - Missing LaTeX packages
    echo - Missing figure files
    echo - Compilation errors
)

pause
