import PyPDF2
import sys

def extract_text_from_pdf(pdf_path):
    text = ""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += f"\n--- PAGE {page_num + 1} ---\n"
                text += page.extract_text()
                text += "\n"
                
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return None
    
    return text

if __name__ == "__main__":
    pdf_file = "Phase 1 Clinical Trials_ A Comprehensive Overview for Clinical Pharmacologists.pdf"
    extracted_text = extract_text_from_pdf(pdf_file)
    
    if extracted_text:
        with open("extracted_text.txt", "w", encoding="utf-8") as f:
            f.write(extracted_text)
        print("Text extracted successfully to extracted_text.txt")
    else:
        print("Failed to extract text from PDF")