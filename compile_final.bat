@echo off
echo Compiling Phase 1 Clinical Trials Comprehensive Document...
echo.

pdflatex phase1_comprehensive_full_fixed.tex
if %errorlevel% neq 0 (
    echo First pass completed with warnings/errors. Running second pass...
) else (
    echo First pass completed successfully. Running second pass...
)

pdflatex phase1_comprehensive_full_fixed.tex
if %errorlevel% neq 0 (
    echo Second pass completed with warnings/errors. Running third pass...
) else (
    echo Second pass completed successfully. Running third pass...
)

pdflatex phase1_comprehensive_full_fixed.tex
if %errorlevel% neq 0 (
    echo Compilation completed with some warnings/errors.
    echo Check the .log file for details.
) else (
    echo Compilation completed successfully!
)

echo.
echo Output file: phase1_comprehensive_full_fixed.pdf
echo.
pause
