import PyPDF2
import re
import sys

def extract_text_from_pdf(pdf_path):
    """Extract text from PDF file"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    except Exception as e:
        print(f"Error reading PDF: {e}")
        return None

def count_figure_placeholders(text):
    """Count various types of figure placeholders in the text"""
    if not text:
        return 0, []
    
    # Convert to lowercase for case-insensitive matching
    text_lower = text.lower()
    
    # Different patterns for figure placeholders
    patterns = [
        r'\bfigure\s+\d+',              # "Figure 1", "Figure 2", etc.
        r'\bfig\.\s*\d+',               # "Fig. 1", "Fig. 2", etc.
        r'\bfig\s+\d+',                 # "Fig 1", "Fig 2", etc.
        r'\[figure\s+\d+\]',            # "[Figure 1]", "[Figure 2]", etc.
        r'\(figure\s+\d+\)',            # "(Figure 1)", "(Figure 2)", etc.
        r'\bfigure\s+[a-z]',            # "Figure A", "Figure B", etc.
        r'\bfig\.\s*[a-z]',             # "Fig. A", "Fig. B", etc.
        r'\bfig\s+[a-z]',               # "Fig A", "Fig B", etc.
        r'\[fig\.\s*\d+\]',             # "[Fig. 1]", "[Fig. 2]", etc.
        r'\(fig\.\s*\d+\)',             # "(Fig. 1)", "(Fig. 2)", etc.
        r'\[fig\s+\d+\]',               # "[Fig 1]", "[Fig 2]", etc.
        r'\(fig\s+\d+\)',               # "(Fig 1)", "(Fig 2)", etc.
    ]
    
    all_matches = []
    for pattern in patterns:
        matches = re.findall(pattern, text_lower)
        all_matches.extend(matches)
    
    # Remove duplicates while preserving order
    unique_matches = []
    seen = set()
    for match in all_matches:
        if match not in seen:
            unique_matches.append(match)
            seen.add(match)
    
    return len(unique_matches), unique_matches

def main():
    pdf_path = "Phase 1 Clinical Trials_ A Comprehensive Overview for Clinical Pharmacologists.pdf"
    
    print(f"Extracting text from: {pdf_path}")
    text = extract_text_from_pdf(pdf_path)
    
    if text:
        count, matches = count_figure_placeholders(text)
        print(f"\nTotal figure placeholders found: {count}")
        
        if matches:
            print("\nFigure placeholders found:")
            for i, match in enumerate(matches, 1):
                print(f"{i}. {match}")
        else:
            print("No figure placeholders found.")
            
        # Save extracted text for manual verification if needed
        with open("extracted_pdf_text.txt", "w", encoding="utf-8") as f:
            f.write(text)
        print(f"\nExtracted text saved to: extracted_pdf_text.txt")
    else:
        print("Failed to extract text from PDF")

if __name__ == "__main__":
    main()
