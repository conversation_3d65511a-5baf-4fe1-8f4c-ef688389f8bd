import fitz  # PyMuPDF
import re
import sys

def extract_text_from_pdf_pymupdf(pdf_path):
    """Extract text from PDF file using PyMuPDF"""
    try:
        doc = fitz.open(pdf_path)
        text = ""
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text += page.get_text() + "\n"
        doc.close()
        return text
    except Exception as e:
        print(f"Error reading PDF with PyMuPDF: {e}")
        return None

def count_figure_placeholders_comprehensive(text):
    """Count various types of figure placeholders in the text with comprehensive patterns"""
    if not text:
        return 0, []
    
    # Convert to lowercase for case-insensitive matching
    text_lower = text.lower()
    
    # More comprehensive patterns for figure placeholders
    patterns = [
        r'\bfigure\s+\d+',              # "Figure 1", "Figure 2", etc.
        r'\bfig\.\s*\d+',               # "Fig. 1", "Fig. 2", etc.
        r'\bfig\s+\d+',                 # "Fig 1", "Fig 2", etc.
        r'\[figure\s+\d+\]',            # "[Figure 1]", "[Figure 2]", etc.
        r'\(figure\s+\d+\)',            # "(Figure 1)", "(Figure 2)", etc.
        r'\bfigure\s+[a-z]',            # "Figure A", "Figure B", etc.
        r'\bfig\.\s*[a-z]',             # "Fig. A", "Fig. B", etc.
        r'\bfig\s+[a-z]',               # "Fig A", "Fig B", etc.
        r'\[fig\.\s*\d+\]',             # "[Fig. 1]", "[Fig. 2]", etc.
        r'\(fig\.\s*\d+\)',             # "(Fig. 1)", "(Fig. 2)", etc.
        r'\[fig\s+\d+\]',               # "[Fig 1]", "[Fig 2]", etc.
        r'\(fig\s+\d+\)',               # "(Fig 1)", "(Fig 2)", etc.
        r'\bfigure\s+\d+\.\d+',         # "Figure 1.1", "Figure 2.3", etc.
        r'\bfig\.\s*\d+\.\d+',          # "Fig. 1.1", "Fig. 2.3", etc.
        r'\bfig\s+\d+\.\d+',            # "Fig 1.1", "Fig 2.3", etc.
        r'\bfigure\s+\d+[a-z]',         # "Figure 1a", "Figure 2b", etc.
        r'\bfig\.\s*\d+[a-z]',          # "Fig. 1a", "Fig. 2b", etc.
        r'\bfig\s+\d+[a-z]',            # "Fig 1a", "Fig 2b", etc.
        r'\[figure\s+\d+[a-z]\]',       # "[Figure 1a]", "[Figure 2b]", etc.
        r'\(figure\s+\d+[a-z]\)',       # "(Figure 1a)", "(Figure 2b)", etc.
        r'\[fig\.\s*\d+[a-z]\]',        # "[Fig. 1a]", "[Fig. 2b]", etc.
        r'\(fig\.\s*\d+[a-z]\)',        # "(Fig. 1a)", "(Fig. 2b)", etc.
    ]
    
    all_matches = []
    for pattern in patterns:
        matches = re.findall(pattern, text_lower)
        all_matches.extend(matches)
    
    # Remove duplicates while preserving order
    unique_matches = []
    seen = set()
    for match in all_matches:
        if match not in seen:
            unique_matches.append(match)
            seen.add(match)
    
    return len(unique_matches), unique_matches

def search_for_figure_context(text):
    """Search for lines containing figure references to provide context"""
    if not text:
        return []
    
    lines = text.split('\n')
    figure_lines = []
    
    for i, line in enumerate(lines):
        if re.search(r'\bfig(?:ure)?\.?\s*\d+', line, re.IGNORECASE):
            # Include some context around the figure reference
            start = max(0, i-1)
            end = min(len(lines), i+2)
            context = '\n'.join(lines[start:end])
            figure_lines.append(f"Line {i+1}: {context}")
    
    return figure_lines

def main():
    pdf_path = "Phase 1 Clinical Trials_ A Comprehensive Overview for Clinical Pharmacologists.pdf"
    
    print(f"Extracting text from: {pdf_path}")
    
    # Try PyMuPDF first
    text = extract_text_from_pdf_pymupdf(pdf_path)
    
    if text:
        count, matches = count_figure_placeholders_comprehensive(text)
        print(f"\nTotal figure placeholders found: {count}")
        
        if matches:
            print("\nFigure placeholders found:")
            for i, match in enumerate(matches, 1):
                print(f"{i}. {match}")
        else:
            print("No figure placeholders found.")
        
        # Search for figure context
        figure_contexts = search_for_figure_context(text)
        if figure_contexts:
            print(f"\nFigure references with context ({len(figure_contexts)} found):")
            for i, context in enumerate(figure_contexts, 1):
                print(f"\n{i}. {context}")
                print("-" * 50)
        
        # Save extracted text for manual verification
        with open("extracted_pdf_text_pymupdf.txt", "w", encoding="utf-8") as f:
            f.write(text)
        print(f"\nExtracted text saved to: extracted_pdf_text_pymupdf.txt")
        
        # Also search for any placeholder text or missing figure indicators
        placeholder_patterns = [
            r'\[insert\s+figure\]',
            r'\[figure\s+here\]',
            r'\[placeholder\]',
            r'figure\s+placeholder',
            r'insert\s+figure',
            r'figure\s+missing',
            r'<figure>',
            r'&lt;figure&gt;',
        ]
        
        placeholder_matches = []
        for pattern in placeholder_patterns:
            matches = re.findall(pattern, text.lower())
            placeholder_matches.extend(matches)
        
        if placeholder_matches:
            print(f"\nPlaceholder text found: {len(placeholder_matches)}")
            for match in placeholder_matches:
                print(f"- {match}")
    else:
        print("Failed to extract text from PDF")

if __name__ == "__main__":
    main()
