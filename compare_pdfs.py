import fitz  # PyMuPDF
import re
import difflib

def extract_text_from_pdf(pdf_path):
    """Extract text from PDF file using PyMuPDF"""
    try:
        doc = fitz.open(pdf_path)
        text = ""
        page_texts = []
        page_count = len(doc)
        for page_num in range(page_count):
            page = doc.load_page(page_num)
            page_text = page.get_text()
            page_texts.append(f"=== PAGE {page_num + 1} ===\n{page_text}\n")
            text += page_text + "\n"
        doc.close()
        return text, page_texts, page_count
    except Exception as e:
        print(f"Error reading PDF {pdf_path}: {e}")
        return None, None, 0

def compare_documents():
    """Compare the original and improved PDF documents"""
    
    # Extract text from both PDFs
    print("Extracting text from original PDF...")
    original_text, original_pages, original_page_count = extract_text_from_pdf(
        "Phase 1 Clinical Trials_ A Comprehensive Overview for Clinical Pharmacologists.pdf"
    )
    
    print("Extracting text from improved PDF...")
    improved_text, improved_pages, improved_page_count = extract_text_from_pdf(
        "phase1_clinical_trials_improved.pdf"
    )
    
    if not original_text or not improved_text:
        print("Failed to extract text from one or both PDFs")
        return
    
    # Basic statistics
    print(f"\n=== DOCUMENT COMPARISON ===")
    print(f"Original PDF: {original_page_count} pages, {len(original_text)} characters")
    print(f"Improved PDF: {improved_page_count} pages, {len(improved_text)} characters")
    print(f"Character difference: {len(original_text) - len(improved_text)}")
    
    # Word count comparison
    original_words = len(original_text.split())
    improved_words = len(improved_text.split())
    print(f"Original word count: {original_words}")
    print(f"Improved word count: {improved_words}")
    print(f"Word difference: {original_words - improved_words}")
    
    # Save extracted texts for detailed analysis
    with open("original_pdf_text.txt", "w", encoding="utf-8") as f:
        f.write(original_text)
    
    with open("improved_pdf_text.txt", "w", encoding="utf-8") as f:
        f.write(improved_text)
    
    # Create a detailed comparison
    print(f"\n=== DETAILED COMPARISON ===")
    
    # Split into lines for comparison
    original_lines = original_text.split('\n')
    improved_lines = improved_text.split('\n')
    
    # Generate diff
    diff = list(difflib.unified_diff(
        improved_lines, 
        original_lines, 
        fromfile='improved_pdf', 
        tofile='original_pdf', 
        lineterm=''
    ))
    
    # Save diff to file
    with open("content_diff.txt", "w", encoding="utf-8") as f:
        f.write('\n'.join(diff))
    
    # Analyze major sections
    print("\n=== SECTION ANALYSIS ===")
    
    # Look for major sections in both documents
    section_patterns = [
        r'Introduction',
        r'Objectives of Phase 1 Trials',
        r'Key Steps in Planning',
        r'Study Designs',
        r'Dose Selection',
        r'Safety Monitoring',
        r'Statistical Analysis',
        r'Regulatory',
        r'Limitations',
        r'Conclusion'
    ]
    
    for pattern in section_patterns:
        original_matches = len(re.findall(pattern, original_text, re.IGNORECASE))
        improved_matches = len(re.findall(pattern, improved_text, re.IGNORECASE))
        status = "✓" if original_matches == improved_matches else "⚠"
        print(f"{status} {pattern}: Original={original_matches}, Improved={improved_matches}")
    
    # Look for specific content that might be missing
    print("\n=== MISSING CONTENT ANALYSIS ===")
    
    # Check for specific phrases/sections from the original
    key_phrases = [
        "TGN1412",
        "Fialuridine",
        "3+3 design",
        "Continual Reassessment Method",
        "NOAEL",
        "MABEL",
        "pharmacokinetic",
        "dose-limiting toxicity",
        "maximum tolerated dose",
        "Good Clinical Practice"
    ]
    
    missing_content = []
    for phrase in key_phrases:
        if phrase.lower() in original_text.lower() and phrase.lower() not in improved_text.lower():
            missing_content.append(phrase)
            print(f"⚠ Missing: {phrase}")
        elif phrase.lower() in original_text.lower() and phrase.lower() in improved_text.lower():
            print(f"✓ Present: {phrase}")
    
    if missing_content:
        print(f"\nFound {len(missing_content)} key phrases missing in improved version")
    else:
        print("\nAll key phrases found in both versions")
    
    # Summary
    print(f"\n=== SUMMARY ===")
    if len(original_text) > len(improved_text):
        print(f"⚠ The improved PDF has lost {len(original_text) - len(improved_text)} characters")
        print(f"⚠ The improved PDF has lost {original_words - improved_words} words")
        print("✓ Content recovery needed")
    else:
        print("✓ No significant content loss detected")
    
    return original_text, improved_text, original_pages, improved_pages

def main():
    """Main function to compare PDFs"""
    compare_documents()

if __name__ == "__main__":
    main()
