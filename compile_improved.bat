@echo off
echo ======================================
echo  Phase 1 Clinical Trials LaTeX Compiler
echo ======================================
echo.

echo Checking for LaTeX installation...
where pdflatex >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: pdflatex not found. Please install a LaTeX distribution:
    echo   - MiKTeX: https://miktex.org/
    echo   - TeX Live: https://www.tug.org/texlive/
    echo.
    pause
    exit /b 1
)

echo LaTeX found. Compiling document...
echo.

echo First compilation pass...
pdflatex -interaction=nonstopmode phase1_clinical_trials_improved.tex
if %errorlevel% neq 0 (
    echo ERROR: First compilation failed. Check the log file.
    pause
    exit /b 1
)

echo Second compilation pass (for cross-references)...
pdflatex -interaction=nonstopmode phase1_clinical_trials_improved.tex
if %errorlevel% neq 0 (
    echo ERROR: Second compilation failed. Check the log file.
    pause
    exit /b 1
)

echo.
echo ======================================
echo  Compilation Complete!
echo ======================================
echo.
echo Output file: phase1_clinical_trials_improved.pdf
echo.
echo Generated figures:
echo   - integrated_phase1_design.png
echo   - dose_escalation_methods.png
echo   - pk_profiles.png
echo   - safety_monitoring.png
echo.

echo Cleaning up auxiliary files...
del *.aux *.log *.out *.toc 2>nul

echo Done! Opening PDF...
start phase1_clinical_trials_improved.pdf

pause
