
@echo off
echo ======================================
echo  Comprehensive Phase 1 Clinical Trials LaTeX Compiler
echo ======================================
echo.

echo Checking for LaTeX installation...
where pdflatex >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: pdflatex not found. Please install a LaTeX distribution.
    pause
    exit /b 1
)

echo LaTeX found. Compiling comprehensive document...
echo.

echo First compilation pass...
pdflatex -interaction=nonstopmode phase1_clinical_trials_comprehensive.tex
if %errorlevel% neq 0 (
    echo WARNING: First compilation had issues. Continuing...
)

echo Second compilation pass (for cross-references)...
pdflatex -interaction=nonstopmode phase1_clinical_trials_comprehensive.tex
if %errorlevel% neq 0 (
    echo WARNING: Second compilation had issues. Continuing...
)

echo Third compilation pass (for final references)...
pdflatex -interaction=nonstopmode phase1_clinical_trials_comprehensive.tex

echo.
echo ======================================
echo  Compilation Complete!
echo ======================================
echo.
echo Output file: phase1_clinical_trials_comprehensive.pdf
echo.

echo Cleaning up auxiliary files...
del *.aux *.log *.out *.toc 2>nul

echo Done! Opening PDF...
start phase1_clinical_trials_comprehensive.pdf

pause
